import 'package:flutter/material.dart';
import 'package:firebase_ui_auth/firebase_ui_auth.dart';
import 'package:firebase_ui_oauth_google/firebase_ui_oauth_google.dart';
import 'package:firebase_auth/firebase_auth.dart' hide EmailAuthProvider;
import 'package:upshift/pages/main_navigation.dart';
import 'package:upshift/pages/onboarding.dart';
import '../services/firestore.dart';
import '../models/models.dart' as models;
import '../theme/theme.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // If user is logged in
        if (snapshot.hasData) {
          return AuthenticatedUserHandler(user: snapshot.data!);
        }
        // If user is NOT logged in
        else {
          return const FirebaseUIAuthScreen();
        }
      },
    );
  }
}

class AuthenticatedUserHandler extends StatefulWidget {
  final User user;

  const AuthenticatedUserHandler({super.key, required this.user});

  @override
  State<AuthenticatedUserHandler> createState() =>
      _AuthenticatedUserHandlerState();
}

class _AuthenticatedUserHandlerState extends State<AuthenticatedUserHandler> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<models.User?>(
      future: _checkUserOnboardingStatus(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    AppIcons.error,
                    size: AppDimensions.iconXxl,
                    color: AppColors.error,
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  Text('Error: ${snapshot.error}'),
                  SizedBox(height: AppDimensions.spacingM),
                  ElevatedButton(
                    onPressed: () => setState(() {}),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        final user = snapshot.data;

        // If user doesn't exist in Firestore or is not onboarded, show onboarding
        if (user == null || !user.isOnboarded) {
          return const OnboardingPage();
        }

        // User exists and is onboarded, show main app
        return const MainNavigationPage();
      },
    );
  }

  Future<models.User?> _checkUserOnboardingStatus() async {
    try {
      // First, try to get the user from Firestore
      final firestoreUser = await FirestoreService.getUser(widget.user.uid);

      if (firestoreUser == null) {
        // User doesn't exist in Firestore, create them
        final newUser = models.User(
          id: widget.user.uid,
          name:
              widget.user.displayName ??
              widget.user.email?.split('@').first ??
              'User',
          email: widget.user.email ?? '',
          isOnboarded: false,
          isAdmin: false, // Default to non-admin
          description: null,
          preferredPersonaIds: const [],
          createdAt: DateTime.now(),
        );

        await FirestoreService.createOrUpdateUser(newUser);
        return newUser;
      }

      return firestoreUser;
    } catch (e) {
      throw Exception('Failed to check user status: $e');
    }
  }
}

class FirebaseUIAuthScreen extends StatelessWidget {
  const FirebaseUIAuthScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: AppDimensions.paddingL,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Logo
              Center(
                child: Image.asset(
                  'assets/upshift-text-logo.png',
                  height: 48,
                  fit: BoxFit.contain,
                ),
              ),
              SizedBox(height: AppDimensions.spacingL),

              // Welcome text
              Center(
                child: Text(
                  'Welcome back to Upshift!',
                  style: AppTypography.textTheme.headlineSmall,
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: AppDimensions.spacingXl),

              // Email authentication form
              FirebaseUIActions(
                actions: [
                  AuthStateChangeAction<SignedIn>((context, state) {
                    // Handle successful sign-in - this will be handled by the parent widget
                  }),
                ],
                child: LoginView(
                  action: AuthAction.signIn,
                  providers: [EmailAuthProvider()],
                ),
              ),

              // Significant spacing between email form and Google button
              SizedBox(height: AppDimensions.spacingXxl),

              // Divider with "OR" text
              Row(
                children: [
                  Expanded(child: Divider(color: AppColors.gray300)),
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.spacingM,
                    ),
                    child: Text(
                      'OR',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: AppColors.gray500,
                      ),
                    ),
                  ),
                  Expanded(child: Divider(color: AppColors.gray300)),
                ],
              ),

              // More spacing after divider
              SizedBox(height: AppDimensions.spacingXl),

              // Google Sign-In button
              FirebaseUIActions(
                actions: [
                  AuthStateChangeAction<SignedIn>((context, state) {
                    // Handle successful sign-in - this will be handled by the parent widget
                  }),
                ],
                child: OAuthProviderButton(
                  provider: GoogleProvider(
                    clientId:
                        '49879574938-k2mk9104v7uo81elbfrn5e71qoa1bm3p.apps.googleusercontent.com',
                  ),
                ),
              ),

              // Footer spacing and terms
              SizedBox(height: AppDimensions.spacingXl),
              Center(
                child: Text(
                  'By signing in, you agree to our terms and conditions.',
                  style: AppTypography.textTheme.bodySmall?.copyWith(
                    color: AppColors.gray500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
