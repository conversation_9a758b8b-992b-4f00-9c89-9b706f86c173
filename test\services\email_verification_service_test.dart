import 'package:flutter_test/flutter_test.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:upshift/services/email_verification_service.dart';

// Generate mocks for Firebase Auth classes
@GenerateMocks([
  FirebaseAuth,
  User,
  UserMetadata,
  UserInfo,
])
import 'email_verification_service_test.mocks.dart';

void main() {
  group('EmailVerificationService', () {
    late MockFirebaseAuth mockAuth;
    late MockUser mockUser;
    late MockUserMetadata mockMetadata;

    setUp(() {
      mockAuth = MockFirebaseAuth();
      mockUser = MockUser();
      mockMetadata = MockUserMetadata();
    });

    group('isEmailVerified', () {
      test('returns true when user email is verified', () {
        // Arrange
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.emailVerified).thenReturn(true);

        // Act
        final result = EmailVerificationService.isEmailVerified();

        // Assert
        expect(result, true);
      });

      test('returns false when user email is not verified', () {
        // Arrange
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.emailVerified).thenReturn(false);

        // Act
        final result = EmailVerificationService.isEmailVerified();

        // Assert
        expect(result, false);
      });

      test('returns false when no user is signed in', () {
        // Arrange
        when(mockAuth.currentUser).thenReturn(null);

        // Act
        final result = EmailVerificationService.isEmailVerified();

        // Assert
        expect(result, false);
      });
    });

    group('getCurrentUserEmail', () {
      test('returns user email when user is signed in', () {
        // Arrange
        const testEmail = '<EMAIL>';
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.email).thenReturn(testEmail);

        // Act
        final result = EmailVerificationService.getCurrentUserEmail();

        // Assert
        expect(result, testEmail);
      });

      test('returns null when no user is signed in', () {
        // Arrange
        when(mockAuth.currentUser).thenReturn(null);

        // Act
        final result = EmailVerificationService.getCurrentUserEmail();

        // Assert
        expect(result, null);
      });
    });

    group('needsEmailVerification', () {
      test('returns true for unverified email/password user', () {
        // Arrange
        final mockUserInfo = MockUserInfo();
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.emailVerified).thenReturn(false);
        when(mockUser.email).thenReturn('<EMAIL>');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUserInfo.providerId).thenReturn('password');

        // Act
        final result = EmailVerificationService.needsEmailVerification();

        // Assert
        expect(result, true);
      });

      test('returns false for verified user', () {
        // Arrange
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.emailVerified).thenReturn(true);

        // Act
        final result = EmailVerificationService.needsEmailVerification();

        // Assert
        expect(result, false);
      });

      test('returns false when no user is signed in', () {
        // Arrange
        when(mockAuth.currentUser).thenReturn(null);

        // Act
        final result = EmailVerificationService.needsEmailVerification();

        // Assert
        expect(result, false);
      });

      test('returns false for OAuth user (Google)', () {
        // Arrange
        final mockUserInfo = MockUserInfo();
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.emailVerified).thenReturn(false);
        when(mockUser.email).thenReturn('<EMAIL>');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUserInfo.providerId).thenReturn('google.com');

        // Act
        final result = EmailVerificationService.needsEmailVerification();

        // Assert
        expect(result, false);
      });

      test('returns false when user has no email', () {
        // Arrange
        final mockUserInfo = MockUserInfo();
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.emailVerified).thenReturn(false);
        when(mockUser.email).thenReturn(null);
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUserInfo.providerId).thenReturn('password');

        // Act
        final result = EmailVerificationService.needsEmailVerification();

        // Assert
        expect(result, false);
      });
    });

    group('getUserCreationTime', () {
      test('returns creation time when user is signed in', () {
        // Arrange
        final testTime = DateTime.now();
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.metadata).thenReturn(mockMetadata);
        when(mockMetadata.creationTime).thenReturn(testTime);

        // Act
        final result = EmailVerificationService.getUserCreationTime();

        // Assert
        expect(result, testTime);
      });

      test('returns null when no user is signed in', () {
        // Arrange
        when(mockAuth.currentUser).thenReturn(null);

        // Act
        final result = EmailVerificationService.getUserCreationTime();

        // Assert
        expect(result, null);
      });
    });

    group('isRecentRegistration', () {
      test('returns true for user created within 5 minutes', () {
        // Arrange
        final recentTime = DateTime.now().subtract(const Duration(minutes: 3));
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.metadata).thenReturn(mockMetadata);
        when(mockMetadata.creationTime).thenReturn(recentTime);

        // Act
        final result = EmailVerificationService.isRecentRegistration();

        // Assert
        expect(result, true);
      });

      test('returns false for user created more than 5 minutes ago', () {
        // Arrange
        final oldTime = DateTime.now().subtract(const Duration(minutes: 10));
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.metadata).thenReturn(mockMetadata);
        when(mockMetadata.creationTime).thenReturn(oldTime);

        // Act
        final result = EmailVerificationService.isRecentRegistration();

        // Assert
        expect(result, false);
      });

      test('returns false when no user is signed in', () {
        // Arrange
        when(mockAuth.currentUser).thenReturn(null);

        // Act
        final result = EmailVerificationService.isRecentRegistration();

        // Assert
        expect(result, false);
      });
    });
  });
}
