import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

/// Service class for handling email verification operations
/// 
/// This service provides methods to:
/// - Send email verification
/// - Check verification status
/// - Resend verification emails
/// - Handle verification state changes
class EmailVerificationService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Send email verification to the current user
  /// 
  /// Returns true if the email was sent successfully, false otherwise
  /// Throws an exception if no user is signed in or if sending fails
  static Future<bool> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('No user is currently signed in');
      }

      if (user.emailVerified) {
        debugPrint('User email is already verified');
        return true;
      }

      await user.sendEmailVerification();
      debugPrint('Email verification sent to: ${user.email}');
      return true;
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth error sending verification email: ${e.code} - ${e.message}');
      
      switch (e.code) {
        case 'too-many-requests':
          throw Exception('Too many verification emails sent. Please wait before requesting another.');
        case 'user-disabled':
          throw Exception('This account has been disabled.');
        case 'invalid-email':
          throw Exception('The email address is invalid.');
        default:
          throw Exception('Failed to send verification email: ${e.message}');
      }
    } catch (e) {
      debugPrint('Error sending verification email: $e');
      throw Exception('Failed to send verification email: $e');
    }
  }

  /// Check if the current user's email is verified
  /// 
  /// Returns true if verified, false if not verified or no user signed in
  static bool isEmailVerified() {
    final user = _auth.currentUser;
    return user?.emailVerified ?? false;
  }

  /// Reload the current user to get the latest verification status
  /// 
  /// This is useful after the user has clicked the verification link
  /// Returns the updated verification status
  static Future<bool> reloadUserAndCheckVerification() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      await user.reload();
      final updatedUser = _auth.currentUser;
      final isVerified = updatedUser?.emailVerified ?? false;
      
      debugPrint('User verification status after reload: $isVerified');
      return isVerified;
    } catch (e) {
      debugPrint('Error reloading user: $e');
      return false;
    }
  }

  /// Get the current user's email address
  /// 
  /// Returns null if no user is signed in
  static String? getCurrentUserEmail() {
    return _auth.currentUser?.email;
  }

  /// Check if the current user needs email verification
  /// 
  /// Returns true if:
  /// - User is signed in
  /// - User has an email address
  /// - Email is not verified
  /// - User signed up with email/password (not OAuth)
  static bool needsEmailVerification() {
    final user = _auth.currentUser;
    if (user == null || user.emailVerified) {
      return false;
    }

    // Check if user signed up with email/password
    // OAuth users (Google, etc.) are automatically verified
    final hasEmailProvider = user.providerData.any(
      (provider) => provider.providerId == 'password',
    );

    return hasEmailProvider && user.email != null;
  }

  /// Resend verification email with rate limiting check
  /// 
  /// Returns true if email was sent, false if rate limited
  /// Throws exception on other errors
  static Future<bool> resendVerificationEmail() async {
    try {
      return await sendEmailVerification();
    } on Exception catch (e) {
      if (e.toString().contains('Too many verification emails')) {
        return false; // Rate limited
      }
      rethrow; // Other errors
    }
  }

  /// Listen to auth state changes for verification status
  /// 
  /// Returns a stream that emits true when email becomes verified
  static Stream<bool> get verificationStatusStream {
    return _auth.authStateChanges().asyncMap((user) async {
      if (user == null) return false;
      
      // Reload user to get latest verification status
      try {
        await user.reload();
        return _auth.currentUser?.emailVerified ?? false;
      } catch (e) {
        debugPrint('Error reloading user in stream: $e');
        return user.emailVerified;
      }
    });
  }

  /// Sign out the current user
  /// 
  /// Useful when user wants to sign in with a different account
  static Future<void> signOut() async {
    try {
      await _auth.signOut();
      debugPrint('User signed out successfully');
    } catch (e) {
      debugPrint('Error signing out: $e');
      throw Exception('Failed to sign out: $e');
    }
  }

  /// Get user creation time to determine if this is a new registration
  /// 
  /// Returns null if no user is signed in
  static DateTime? getUserCreationTime() {
    return _auth.currentUser?.metadata.creationTime;
  }

  /// Check if this is a recent registration (within last 5 minutes)
  /// 
  /// Useful for determining if we should automatically send verification
  static bool isRecentRegistration() {
    final creationTime = getUserCreationTime();
    if (creationTime == null) return false;
    
    final now = DateTime.now();
    final difference = now.difference(creationTime);
    return difference.inMinutes <= 5;
  }
}
